#!/usr/bin/env python3
# 测试save_target_config函数

def save_target_config(x, y, config_file="test_target.txt"):
    """保存target坐标到配置文件"""
    try:
        with open(config_file, 'w') as f:
            f.write(f"target_x={x}\n")
            f.write(f"target_y={y}\n")
        print(f"[成功] 准心坐标已保存到 {config_file}: ({x}, {y})")
        return True
    except Exception as e:
        print(f"[错误] 保存准心坐标失败: {e}")
        return False

# 测试保存功能
if __name__ == "__main__":
    print("测试保存功能...")
    result = save_target_config(100, 150)
    print(f"保存结果: {result}")
    
    # 读取验证
    try:
        with open("test_target.txt", 'r') as f:
            content = f.read()
        print(f"文件内容:\n{content}")
    except Exception as e:
        print(f"读取失败: {e}")
