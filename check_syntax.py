#!/usr/bin/env python3
import ast
import sys

try:
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 尝试解析语法
    ast.parse(content)
    print("✅ 语法检查通过！")
    
except SyntaxError as e:
    print(f"❌ 语法错误:")
    print(f"  文件: {e.filename}")
    print(f"  行号: {e.lineno}")
    print(f"  列号: {e.offset}")
    print(f"  错误: {e.msg}")
    print(f"  代码: {e.text}")
    
except Exception as e:
    print(f"❌ 其他错误: {e}")
