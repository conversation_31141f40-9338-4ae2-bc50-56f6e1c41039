# gray_threshold_tool.py - 灰度调节工具
# 基于MaixPy框架的触摸屏灰度阈值调节工具

from maix import image, display, app, time, camera, touchscreen
import cv2
import numpy as np
import os

# --------------------------- 灰度调节工具类 ---------------------------
class GrayThresholdTool:
    """黑色检测工具类

    提供灰度转换、黑色目标二值化检测和阈值调节功能
    支持触摸屏交互和实时预览
    使用反向二值化检测黑色区域
    """
    
    def __init__(self, initial_threshold=127):
        """初始化灰度调节工具
        
        Args:
            initial_threshold (int): 初始阈值，默认127
        """
        self.threshold = initial_threshold  # 当前阈值
        self.step = 5  # 阈值调节步长
        self.min_threshold = 0  # 最小阈值
        self.max_threshold = 255  # 最大阈值

        # 触摸相关参数
        self.button_positions = {}  # 存储按钮位置信息
        self.last_touch_time = 0  # 上次触摸时间
        self.touch_debounce = 200  # 防抖时间(ms)

        # 调试和性能监控参数
        self.debug_enabled = True  # 调试模式开关
        self.frame_count = 0  # 帧计数器
        self.last_fps_time = 0  # 上次FPS计算时间
        self.fps_interval = 5000  # FPS报告间隔(ms)
        self.total_threshold_changes = 0  # 阈值变化总次数

        # 临时消息显示参数
        self.temp_message = None  # 当前临时消息
        self.temp_message_end = 0  # 消息结束时间

        print(f"黑色检测工具初始化完成，初始阈值: {self.threshold}")
        if self.debug_enabled:
            print("调试模式已启用 - 目标：黑色检测")
    
    def process_image(self, img_cv):
        """处理图像：灰度转换和黑色检测二值化

        Args:
            img_cv: OpenCV格式的BGR图像

        Returns:
            tuple: (灰度图像, 黑色检测二值化图像)
            二值化图像中白色区域表示检测到的黑色目标
        """
        # 添加图像有效性检查
        if img_cv is None or img_cv.size == 0:
            if self.debug_enabled:
                print("警告: 输入图像无效")
            return None, None

        try:
            # 检查图像尺寸
            height, width = img_cv.shape[:2]
            if height == 0 or width == 0:
                if self.debug_enabled:
                    print(f"警告: 图像尺寸无效 {width}x{height}")
                return None, None

            # 灰度转换
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # 二值化处理 - 目标黑色区域
            _, binary = cv2.threshold(gray, self.threshold, 255, cv2.THRESH_BINARY_INV)

            return gray, binary

        except Exception as e:
            print(f"图像处理错误: {e}")
            if self.debug_enabled:
                print(f"图像信息: shape={img_cv.shape if img_cv is not None else 'None'}")
            return None, None
    
    def adjust_threshold(self, delta):
        """调节阈值
        
        Args:
            delta (int): 阈值变化量（正数增加，负数减少）
            
        Returns:
            int: 调节后的阈值
        """
        old_threshold = self.threshold
        
        # 调节阈值，包含边界检查
        self.threshold = max(self.min_threshold, 
                           min(self.max_threshold, self.threshold + delta))
        
        # 输出调节信息和统计
        if old_threshold != self.threshold:
            self.total_threshold_changes += 1
            action = "增加" if delta > 0 else "减少"
            print(f"阈值调节: {old_threshold} -> {self.threshold} ({action})")
            if self.debug_enabled:
                print(f"累计调节次数: {self.total_threshold_changes}")

        return self.threshold
    
    def get_threshold(self):
        """获取当前阈值
        
        Returns:
            int: 当前阈值
        """
        return self.threshold
    
    def set_threshold(self, value):
        """设置阈值
        
        Args:
            value (int): 新的阈值
            
        Returns:
            int: 设置后的阈值
        """
        old_threshold = self.threshold
        self.threshold = max(self.min_threshold, 
                           min(self.max_threshold, value))
        
        if old_threshold != self.threshold:
            print(f"阈值设置: {old_threshold} -> {self.threshold}")
        
        return self.threshold

    def draw_ui(self, original_img, gray_img, binary_img, disp_width, disp_height):
        """绘制用户界面：只显示二值化结果

        Args:
            original_img: 原始BGR图像（保留参数兼容性）
            gray_img: 灰度图像（保留参数兼容性）
            binary_img: 二值化图像
            disp_width: 显示宽度
            disp_height: 显示高度

        Returns:
            np.ndarray: 显示图像（只包含二值化结果）
        """
        try:
            # 计算图像显示区域（预留底部按钮区域）
            img_height = disp_height - 60

            # 调整二值化图像尺寸到全屏显示
            resized_binary = cv2.resize(binary_img, (disp_width, img_height))

            # 转换二值化图像为3通道
            binary_3ch = cv2.cvtColor(resized_binary, cv2.COLOR_GRAY2BGR)

            # 创建显示图像
            display_img = np.zeros((disp_height, disp_width, 3), dtype=np.uint8)

            # 放置二值化图像（全屏显示）
            display_img[:img_height, :] = binary_3ch

            # 添加标签
            cv2.putText(display_img, "Black Detection", (10, 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

            # 显示临时消息（如保存/加载/重置结果）
            from maix import time
            if self.temp_message and time.ticks_ms() < self.temp_message_end:
                # 计算消息显示位置（屏幕中央）
                text_x = disp_width // 2 - 80
                text_y = disp_height // 2

                # 根据消息内容设置颜色
                if any(keyword in self.temp_message for keyword in ["SAVED", "LOADED", "RESET"]):
                    color = (0, 255, 0)  # 绿色表示成功
                else:
                    color = (0, 0, 255)  # 红色表示失败

                # 绘制临时消息
                cv2.putText(display_img, self.temp_message, (text_x, text_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 1.5, color, 3)

            elif self.temp_message:
                # 消息过期，清除
                self.temp_message = None

            return display_img

        except Exception as e:
            print(f"界面绘制错误: {e}")
            # 返回空白图像
            return np.zeros((disp_height, disp_width, 3), dtype=np.uint8)

    def draw_buttons(self, img, disp_width, disp_height):
        """绘制控制按钮

        Args:
            img: 要绘制按钮的图像
            disp_width: 显示宽度
            disp_height: 显示高度

        Returns:
            dict: 按钮位置信息字典
        """
        try:
            # 定义按钮位置和尺寸
            button_height = 40
            button_width = 60
            y_pos = disp_height - 50

            # 按钮位置信息
            button_positions = {}

            # 绘制阈值显示
            threshold_text = f'Threshold: {self.threshold}'
            cv2.putText(img, threshold_text, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 绘制减少按钮 (-)
            decrease_x = disp_width // 2 - 100
            decrease_y = y_pos - 20
            cv2.rectangle(img, (decrease_x, decrease_y),
                         (decrease_x + button_width, decrease_y + button_height),
                         (0, 0, 255), 2)  # 红色边框
            cv2.putText(img, "-", (decrease_x + 25, decrease_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
            button_positions['decrease'] = (decrease_x, decrease_y, button_width, button_height)

            # 绘制增加按钮 (+)
            increase_x = disp_width // 2 - 30
            increase_y = y_pos - 20
            cv2.rectangle(img, (increase_x, increase_y),
                         (increase_x + button_width, increase_y + button_height),
                         (0, 255, 0), 2)  # 绿色边框
            cv2.putText(img, "+", (increase_x + 22, increase_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
            button_positions['increase'] = (increase_x, increase_y, button_width, button_height)

            # 绘制退出按钮
            exit_x = disp_width - 80
            exit_y = 10
            exit_width = 70
            exit_height = 30
            cv2.rectangle(img, (exit_x, exit_y),
                         (exit_x + exit_width, exit_y + exit_height),
                         (255, 255, 255), 2)  # 白色边框
            cv2.putText(img, "Exit", (exit_x + 15, exit_y + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            button_positions['exit'] = (exit_x, exit_y, exit_width, exit_height)

            # 功能按钮尺寸（增大以便于触摸）
            func_button_width = 70
            func_button_height = 40
            func_button_x = disp_width - 80  # 右侧位置，调整以适应更大按钮

            # 绘制保存按钮 (Save)
            save_y = 50
            cv2.rectangle(img, (func_button_x, save_y),
                         (func_button_x + func_button_width, save_y + func_button_height),
                         (255, 0, 0), 2)  # 蓝色边框
            cv2.putText(img, "Save", (func_button_x + 15, save_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            button_positions['save'] = (func_button_x, save_y, func_button_width, func_button_height)

            # 绘制加载按钮 (Load)
            load_y = save_y + func_button_height + 15
            cv2.rectangle(img, (func_button_x, load_y),
                         (func_button_x + func_button_width, load_y + func_button_height),
                         (255, 255, 0), 2)  # 青色边框
            cv2.putText(img, "Load", (func_button_x + 15, load_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            button_positions['load'] = (func_button_x, load_y, func_button_width, func_button_height)

            # 绘制重置按钮 (Reset)
            reset_y = load_y + func_button_height + 15
            cv2.rectangle(img, (func_button_x, reset_y),
                         (func_button_x + func_button_width, reset_y + func_button_height),
                         (0, 165, 255), 2)  # 橙色边框
            cv2.putText(img, "Reset", (func_button_x + 10, reset_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 165, 255), 2)
            button_positions['reset'] = (func_button_x, reset_y, func_button_width, func_button_height)

            return button_positions

        except Exception as e:
            print(f"按钮绘制错误: {e}")
            return {}

    def setup_buttons(self, disp_width, disp_height):
        """设置按钮位置信息

        Args:
            disp_width: 显示宽度
            disp_height: 显示高度
        """
        # 定义按钮位置和尺寸
        button_height = 40
        button_width = 60
        y_pos = disp_height - 50

        # 功能按钮尺寸和位置（与draw_buttons保持一致）
        func_button_width = 70
        func_button_height = 40
        func_button_x = disp_width - 80

        self.button_positions = {
            'decrease': (disp_width//2 - 100, y_pos - 20, button_width, button_height),
            'increase': (disp_width//2 - 30, y_pos - 20, button_width, button_height),
            'exit': (disp_width - 80, 10, 70, 30),
            'save': (func_button_x, 50, func_button_width, func_button_height),
            'load': (func_button_x, 105, func_button_width, func_button_height),
            'reset': (func_button_x, 160, func_button_width, func_button_height)
        }

        print(f"按钮位置设置完成: {self.button_positions}")

    def is_in_button(self, x, y, button_name):
        """检查触摸点是否在指定按钮内

        Args:
            x: 触摸点x坐标
            y: 触摸点y坐标
            button_name: 按钮名称

        Returns:
            bool: 是否在按钮内
        """
        if button_name not in self.button_positions:
            return False

        bx, by, bw, bh = self.button_positions[button_name]
        return bx <= x <= bx + bw and by <= y <= by + bh

    def handle_touch(self, x, y, pressed, current_time):
        """处理触摸事件

        Args:
            x: 触摸点x坐标
            y: 触摸点y坐标
            pressed: 是否按下
            current_time: 当前时间(ms)

        Returns:
            str or None: 触摸结果('increase', 'decrease', 'exit', 'save', 'load', 'reset', None)
        """
        try:
            # 防抖处理
            if current_time - self.last_touch_time < self.touch_debounce:
                return None

            if pressed:
                self.last_touch_time = current_time

                # 检查按钮点击
                if self.is_in_button(x, y, 'increase'):
                    old_threshold = self.threshold
                    self.adjust_threshold(self.step)
                    print(f"触摸事件: 增加阈值 {old_threshold} -> {self.threshold}")
                    return 'increase'

                elif self.is_in_button(x, y, 'decrease'):
                    old_threshold = self.threshold
                    self.adjust_threshold(-self.step)
                    print(f"触摸事件: 减少阈值 {old_threshold} -> {self.threshold}")
                    return 'decrease'

                elif self.is_in_button(x, y, 'exit'):
                    print(f"触摸事件: 退出按钮")
                    return 'exit'

                elif self.is_in_button(x, y, 'save'):
                    success = self.save_threshold_to_file()
                    print(f"触摸事件: 保存阈值 - {'成功' if success else '失败'}")
                    return 'save'

                elif self.is_in_button(x, y, 'load'):
                    success = self.load_threshold_from_file()
                    print(f"触摸事件: 加载阈值 - {'成功' if success else '失败'}")
                    return 'load'

                elif self.is_in_button(x, y, 'reset'):
                    success = self.reset_threshold()
                    print(f"触摸事件: 重置阈值 - {'成功' if success else '失败'}")
                    return 'reset'

                else:
                    # 触摸在按钮外
                    print(f"触摸事件: 位置({x},{y}) 不在任何按钮内")

            return None

        except Exception as e:
            print(f"触摸处理错误: {e}")
            return None

    def log_threshold_change(self, old_value, new_value, action):
        """记录阈值变化日志

        Args:
            old_value: 旧阈值
            new_value: 新阈值
            action: 操作类型
        """
        if self.debug_enabled:
            print(f"阈值调节: {old_value} -> {new_value} ({action})")

    def log_touch_event(self, x, y, button_name):
        """记录触摸事件日志

        Args:
            x: 触摸x坐标
            y: 触摸y坐标
            button_name: 按钮名称
        """
        if self.debug_enabled:
            print(f"触摸事件: 位置({x},{y}) 按钮({button_name})")

    def update_fps_monitor(self, current_time):
        """更新FPS监控

        Args:
            current_time: 当前时间(ms)
        """
        self.frame_count += 1
        if current_time - self.last_fps_time > self.fps_interval:
            if self.last_fps_time > 0:  # 避免第一次计算
                fps = self.frame_count * 1000 / (current_time - self.last_fps_time)
                print(f"性能监控 - FPS: {fps:.1f}, 当前阈值: {self.threshold}, 调节次数: {self.total_threshold_changes}")
            self.frame_count = 0
            self.last_fps_time = current_time

    def get_debug_info(self):
        """获取调试信息

        Returns:
            dict: 调试信息字典
        """
        return {
            'threshold': self.threshold,
            'total_changes': self.total_threshold_changes,
            'debug_enabled': self.debug_enabled,
            'touch_debounce': self.touch_debounce,
            'fps_interval': self.fps_interval
        }

    def set_debug_mode(self, enabled):
        """设置调试模式

        Args:
            enabled: 是否启用调试模式
        """
        self.debug_enabled = enabled
        print(f"调试模式: {'启用' if enabled else '禁用'}")

    def save_threshold_to_file(self, file_path="/root/gray.txt"):
        """保存当前阈值到文件

        Args:
            file_path (str): 保存文件的路径，默认为"/root/gray.txt"

        Returns:
            bool: 保存是否成功
        """
        try:
            with open(file_path, 'w') as f:
                f.write(f"threshold={self.threshold}\n")

            print(f"[成功] 阈值已保存到 {file_path}")
            self.show_temp_message("SAVED", 1000)
            return True

        except (IOError, OSError) as e:
            print(f"[错误] 保存阈值失败: {e}")
            self.show_temp_message("SAVE FAILED", 1000)
            return False
        except Exception as e:
            print(f"[错误] 保存过程发生未知错误: {e}")
            self.show_temp_message("SAVE FAILED", 1000)
            return False

    def load_threshold_from_file(self, file_path="/root/gray.txt"):
        """从文件加载阈值

        Args:
            file_path (str): 文件路径，默认为"/root/gray.txt"

        Returns:
            bool: 加载是否成功
        """
        try:
            import os
            if not os.path.exists(file_path):
                print(f"[错误] 加载失败，文件不存在: {file_path}")
                self.show_temp_message("LOAD FAILED", 1000)
                return False

            with open(file_path, 'r') as f:
                lines = f.readlines()

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    if '=' in line:
                        key, value = line.split('=', 1)
                        if key.strip() == 'threshold':
                            threshold_value = int(value.strip())
                            # 边界检查：确保阈值在0-255范围内
                            if 0 <= threshold_value <= 255:
                                self.threshold = threshold_value
                                print(f"[成功] 已从 {file_path} 加载阈值: {self.threshold}")
                                self.show_temp_message("LOADED", 1000)
                                return True
                            else:
                                print(f"[错误] 阈值超出范围 (0-255): {threshold_value}")
                                self.show_temp_message("LOAD FAILED", 1000)
                                return False

                print(f"[错误] 文件中未找到有效的阈值参数")
                self.show_temp_message("LOAD FAILED", 1000)
                return False

        except (IOError, OSError) as e:
            print(f"[错误] 加载阈值失败: {e}")
            self.show_temp_message("LOAD FAILED", 1000)
            return False
        except ValueError as e:
            print(f"[错误] 阈值格式错误: {e}")
            self.show_temp_message("LOAD FAILED", 1000)
            return False
        except Exception as e:
            print(f"[错误] 加载过程发生未知错误: {e}")
            self.show_temp_message("LOAD FAILED", 1000)
            return False

    def reset_threshold(self):
        """重置阈值到默认值

        Returns:
            bool: 重置是否成功
        """
        try:
            self.threshold = 127  # 重置到默认值
            print("[成功] 阈值已重置到默认值: 127")
            self.show_temp_message("RESET", 1000)
            return True
        except Exception as e:
            print(f"[错误] 重置阈值失败: {e}")
            self.show_temp_message("RESET FAILED", 1000)
            return False

    def show_temp_message(self, text, duration=1000):
        """显示临时消息（用于操作反馈）

        Args:
            text (str): 要显示的消息文本
            duration (int): 显示持续时间（毫秒），默认1000ms
        """
        from maix import time
        self.temp_message = text
        self.temp_message_end = time.ticks_ms() + duration
        if self.debug_enabled:
            print(f"临时消息: {text} (持续时间: {duration}ms)")

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 设备初始化（遵循main.py模式）
    try:
        print("开始初始化灰度调节工具...")

        # 初始化显示屏
        disp = display.Display()
        print(f"显示屏初始化成功，尺寸: {disp.width()}x{disp.height()}")

        # 初始化摄像头
        cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
        print("摄像头初始化成功")

        # 初始化触摸屏
        ts = touchscreen.TouchScreen()
        print("触摸屏初始化成功")

        # 初始化灰度调节工具
        tool = GrayThresholdTool()

        # 设置按钮位置
        tool.setup_buttons(disp.width(), disp.height())

        print("黑色检测工具初始化完成！")
        print("使用说明：")
        print("- 全屏显示黑色检测结果")
        print("- 白色区域表示检测到的黑色目标")
        print("- 点击 '+' 按钮增加阈值（检测更多黑色）")
        print("- 点击 '-' 按钮减少阈值（检测更少黑色）")
        print("- 点击 'Save' 按钮保存当前阈值到文件")
        print("- 点击 'Load' 按钮从文件加载阈值")
        print("- 点击 'Reset' 按钮重置阈值到默认值")
        print("- 点击 'Exit' 按钮退出程序")

    except Exception as e:
        print(f"设备初始化失败: {e}")
        exit()

    # 主循环
    print("进入主循环...")
    frame_count = 0

    while not app.need_exit():
        try:
            frame_count += 1

            # 读取图像
            img = cam.read()
            if img is None:
                print("警告: 无法读取摄像头图像")
                time.sleep_ms(100)
                continue

            img_cv = image.image2cv(img, ensure_bgr=True, copy=False)

            # 图像处理
            gray_img, binary_img = tool.process_image(img_cv)
            if gray_img is None or binary_img is None:
                print("警告: 图像处理失败")
                time.sleep_ms(100)
                continue

            # 绘制界面
            display_img = tool.draw_ui(img_cv, gray_img, binary_img,
                                     disp.width(), disp.height())
            button_positions = tool.draw_buttons(display_img,
                                               disp.width(), disp.height())

            # 触摸检测
            x, y, pressed = ts.read()
            current_time = time.ticks_ms()
            touch_result = tool.handle_touch(x, y, pressed, current_time)

            # 记录触摸事件（调试功能）
            if touch_result and touch_result != 'exit':
                tool.log_touch_event(x, y, touch_result)

            # 处理触摸结果
            if touch_result == 'exit':
                print("用户请求退出程序")
                break
            elif touch_result == 'save':
                print("阈值保存操作已执行")
            elif touch_result == 'load':
                print("阈值加载操作已执行")
            elif touch_result == 'reset':
                print("阈值重置操作已执行")

            # 更新FPS监控
            tool.update_fps_monitor(current_time)

            # 显示图像
            img_show = image.cv2image(display_img, bgr=True, copy=False)
            disp.show(img_show)

            # 控制帧率
            time.sleep_ms(10)

        except KeyboardInterrupt:
            print("接收到键盘中断，退出程序")
            break

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(100)

    print("程序正常退出")
    print(f"总共处理了 {frame_count} 帧图像")

    # 输出最终统计信息
    debug_info = tool.get_debug_info()
    print("=== 运行统计 ===")
    print(f"最终阈值: {debug_info['threshold']}")
    print(f"阈值调节次数: {debug_info['total_changes']}")
    print(f"调试模式: {'启用' if debug_info['debug_enabled'] else '禁用'}")
    if frame_count > 0:
        print(f"平均每帧阈值调节: {debug_info['total_changes']/frame_count:.4f}")
    print("感谢使用黑色检测工具！")
